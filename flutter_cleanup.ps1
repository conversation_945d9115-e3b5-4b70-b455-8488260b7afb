# Flutter Projects Cleanup Script
# Deletes temporary and unnecessary files to save space

Write-Host "Starting Flutter projects cleanup..." -ForegroundColor Green

# Find all Flutter projects
$flutterProjects = Get-ChildItem -Path D:\ -Recurse -Name "pubspec.yaml" -ErrorAction SilentlyContinue |
    Where-Object { $_ -notlike "*flutter\*" -and $_ -notlike "*dart-sdk\*" }

$totalSpaceSaved = 0
$projectsProcessed = 0

foreach ($project in $flutterProjects) {
    $projectPath = Split-Path -Parent (Join-Path "D:\" $project)
    $projectName = Split-Path -Leaf $projectPath

    Write-Host "`nProcessing project: $projectName" -ForegroundColor Yellow
    Write-Host "Path: $projectPath"

    # List of folders to delete
    $foldersToDelete = @(
        "build",
        ".dart_tool",
        ".packages",
        "ios/Pods",
        "ios/.symlinks",
        "android/.gradle",
        "android/app/build",
        "android/build",
        "windows/build",
        "linux/build",
        "macos/build",
        "web/build"
    )

    $filesToDelete = @(
        "pubspec.lock",
        "*.log",
        "*.tmp",
        ".flutter-plugins",
        ".flutter-plugins-dependencies",
        ".metadata"
    )

    $projectSpaceSaved = 0

    # Delete folders
    foreach ($folder in $foldersToDelete) {
        $fullPath = Join-Path $projectPath $folder
        if (Test-Path $fullPath) {
            try {
                $size = (Get-ChildItem -Path $fullPath -Recurse -ErrorAction SilentlyContinue |
                        Measure-Object -Property Length -Sum).Sum
                if ($size) {
                    $projectSpaceSaved += $size
                }

                Remove-Item -Path $fullPath -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "  Deleted: $folder" -ForegroundColor Green
            }
            catch {
                Write-Host "  Failed to delete: $folder" -ForegroundColor Red
            }
        }
    }

    # Delete files
    foreach ($filePattern in $filesToDelete) {
        $files = Get-ChildItem -Path $projectPath -Name $filePattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            $fullPath = Join-Path $projectPath $file
            if (Test-Path $fullPath) {
                try {
                    $size = (Get-Item $fullPath).Length
                    $projectSpaceSaved += $size

                    Remove-Item -Path $fullPath -Force -ErrorAction SilentlyContinue
                    Write-Host "  Deleted: $file" -ForegroundColor Green
                }
                catch {
                    Write-Host "  Failed to delete: $file" -ForegroundColor Red
                }
            }
        }
    }

    $totalSpaceSaved += $projectSpaceSaved
    $projectsProcessed++

    $spaceMB = [math]::Round($projectSpaceSaved / 1MB, 2)
    Write-Host "  Space freed from this project: $spaceMB MB" -ForegroundColor Cyan
}

# Display final results
$totalSpaceMB = [math]::Round($totalSpaceSaved / 1MB, 2)
$totalSpaceGB = [math]::Round($totalSpaceSaved / 1GB, 2)

Write-Host "`n" + "="*50 -ForegroundColor Magenta
Write-Host "Cleanup completed!" -ForegroundColor Green
Write-Host "Projects processed: $projectsProcessed" -ForegroundColor Yellow
Write-Host "Total space freed: $totalSpaceMB MB ($totalSpaceGB GB)" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Magenta

Write-Host "`nCleanup completed successfully!" -ForegroundColor Green
